import React, { useEffect, useRef, useState } from 'react';
import {
  <PERSON><PERSON>,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Box,
  Typography,
  Alert,
  CircularProgress,
  TextField,
  Divider,
} from '@mui/material';
import { BrowserMultiFormatReader, NotFoundException } from '@zxing/library';
import { createLogger } from '@services/loggingService';

interface BarcodeScannerProps {
  open: boolean;
  onClose: () => void;
  onScan: (code: string) => void;
}

const BarcodeScanner: React.FC<BarcodeScannerProps> = ({
  open,
  onClose,
  onScan,
}) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const codeReader = useRef<BrowserMultiFormatReader | null>(null);
  const scanningRef = useRef<boolean>(false);
  const scanStartTime = useRef<number>(0);
  const [isScanning, setIsScanning] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [hasPermission, setHasPermission] = useState<boolean | null>(null);
  const [manualBarcode, setManualBarcode] = useState('');

  // Initialize logger for this component
  const logger = useRef(createLogger('BarcodeScanner'));

  useEffect(() => {
    const handleScannerState = async () => {
      if (open) {
        await logger.current.info('Barcode scanner opened');
        await startScanning();
      } else {
        await logger.current.info('Barcode scanner closed');
        await stopScanning();
      }
    };

    handleScannerState();

    return () => {
      // Cleanup on unmount
      const cleanup = async () => {
        await logger.current.debug('Barcode scanner component unmounting');
        await stopScanning();
      };
      cleanup();
    };
  }, [open]);

  const startScanning = async () => {
    scanStartTime.current = Date.now();

    try {
      await logger.current.info('Starting barcode scanning session');

      setError(null);
      setIsScanning(true);
      setHasPermission(null);
      scanningRef.current = true;

      // Check if MediaDevices API is available
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        const errorMsg = 'Camera access is not available in this desktop application. Please use the manual barcode input below or open this application in a web browser for camera scanning.';
        await logger.current.warn('MediaDevices API not available', {
          hasNavigator: !!navigator,
          hasMediaDevices: !!navigator.mediaDevices,
          hasGetUserMedia: !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia)
        });
        throw new Error(errorMsg);
      }

      await logger.current.debug('MediaDevices API available, requesting camera permission');

      // Check for camera permission
      const stream = await navigator.mediaDevices.getUserMedia({ video: true });
      setHasPermission(true);
      await logger.current.info('Camera permission granted');

      // Stop the test stream
      stream.getTracks().forEach(track => track.stop());

      // Initialize the code reader with enhanced hints for better detection
      codeReader.current = new BrowserMultiFormatReader();

      // Configure hints for better barcode detection
      // Use simple configuration for better compatibility
      const hints = new Map();
      hints.set(3, true); // TRY_HARDER - more thorough scanning
      codeReader.current.hints = hints;

      await logger.current.debug('ZXing BrowserMultiFormatReader initialized with enhanced hints');

      // Get available video devices
      const videoInputDevices = await codeReader.current.listVideoInputDevices();

      await logger.current.debug('Video input devices detected', {
        deviceCount: videoInputDevices.length,
        devices: videoInputDevices.map(device => ({
          deviceId: device.deviceId,
          label: device.label,
          kind: device.kind
        }))
      });

      if (videoInputDevices.length === 0) {
        await logger.current.error('No camera devices found');
        throw new Error('No camera devices found');
      }

      // Use the first available camera (usually back camera on mobile)
      const selectedDeviceId = videoInputDevices[0].deviceId;
      await logger.current.info('Selected camera device', {
        deviceId: selectedDeviceId,
        label: videoInputDevices[0].label
      });

      // Start decoding from video element with constraints for better quality
      if (videoRef.current) {
        const constraints = {
          video: {
            deviceId: selectedDeviceId,
            width: { ideal: 1280, min: 640, max: 1920 },
            height: { ideal: 720, min: 480, max: 1080 },
            facingMode: 'environment', // prefer back camera
            frameRate: { ideal: 30, min: 15 }
          }
        };

        await logger.current.debug('Requesting video stream with constraints', { constraints });

        // Start the video stream first
        const stream = await navigator.mediaDevices.getUserMedia(constraints);
        videoRef.current.srcObject = stream;

        // Log actual stream settings
        const videoTrack = stream.getVideoTracks()[0];
        const settings = videoTrack.getSettings();
        await logger.current.info('Video stream initialized', {
          width: settings.width,
          height: settings.height,
          frameRate: settings.frameRate,
          facingMode: settings.facingMode,
          deviceId: settings.deviceId
        });

        // Wait for video to be ready with timeout
        await new Promise((resolve, reject) => {
          if (videoRef.current) {
            const timeout = setTimeout(() => {
              reject(new Error('Video loading timeout'));
            }, 10000); // 10 second timeout

            videoRef.current.onloadedmetadata = () => {
              clearTimeout(timeout);
              resolve(undefined);
            };

            videoRef.current.onerror = (error) => {
              clearTimeout(timeout);
              reject(new Error('Video loading error'));
            };

            videoRef.current.play().catch(reject);
          }
        });

        await logger.current.info('Video element ready for scanning');

        // Start continuous scanning with proper loop and enhanced error handling
        let scanAttempts = 0;
        const maxScanAttempts = 1000; // Prevent infinite loops

        const scanBarcode = async () => {
          if (videoRef.current && codeReader.current && scanningRef.current) {
            scanAttempts++;

            try {
              const result = await codeReader.current.decodeFromVideoElement(videoRef.current);

              if (result && scanningRef.current) {
                const scannedCode = result.getText();
                const scanDuration = Date.now() - scanStartTime.current;

                await logger.current.info('Barcode successfully detected', {
                  code: scannedCode,
                  format: result.getBarcodeFormat(),
                  scanDuration,
                  scanAttempts,
                  resultPoints: result.getResultPoints()?.length || 0
                });

                scanningRef.current = false;
                onScan(scannedCode);
                stopScanning();
                onClose();
                return;
              }
            } catch (error) {
              if (!(error instanceof NotFoundException)) {
                await logger.current.warn('Barcode scanning error', {
                  error: error instanceof Error ? error.message : String(error),
                  scanAttempts,
                  scanDuration: Date.now() - scanStartTime.current
                });
              }
            }

            // Continue scanning if no result and still active
            if (scanningRef.current && scanAttempts < maxScanAttempts) {
              setTimeout(scanBarcode, 100);
            } else if (scanAttempts >= maxScanAttempts) {
              await logger.current.warn('Maximum scan attempts reached', { maxScanAttempts });
              scanningRef.current = false;
            }
          }
        };

        // Start scanning loop
        await logger.current.debug('Starting barcode scanning loop');
        scanBarcode();
      }
    } catch (err) {
      const scanDuration = Date.now() - scanStartTime.current;

      await logger.current.logError(err, 'Failed to start barcode scanning', {
        scanDuration,
        hasPermission,
        isScanning,
        userAgent: navigator.userAgent,
        platform: navigator.platform
      });

      setHasPermission(false);

      if (err instanceof Error) {
        if (err.name === 'NotAllowedError') {
          const errorMsg = 'Camera permission denied. Please allow camera access to scan barcodes.';
          setError(errorMsg);
          await logger.current.warn('Camera permission denied by user');
        } else if (err.name === 'NotFoundError') {
          const errorMsg = 'No camera found. Please ensure your device has a camera.';
          setError(errorMsg);
          await logger.current.error('No camera device found');
        } else if (err.name === 'NotReadableError') {
          const errorMsg = 'Camera is already in use by another application. Please close other camera applications and try again.';
          setError(errorMsg);
          await logger.current.error('Camera already in use');
        } else if (err.name === 'OverconstrainedError') {
          const errorMsg = 'Camera constraints could not be satisfied. Please try with a different camera.';
          setError(errorMsg);
          await logger.current.error('Camera constraints not satisfied');
        } else if (err.message.includes('not supported in this environment')) {
          setError(err.message);
          await logger.current.warn('Environment not supported for camera access');
        } else if (err.message.includes('timeout')) {
          const errorMsg = 'Camera initialization timed out. Please try again.';
          setError(errorMsg);
          await logger.current.error('Camera initialization timeout');
        } else {
          setError(`Failed to access camera: ${err.message}`);
          await logger.current.error('Unknown camera access error', { errorMessage: err.message });
        }
      } else {
        setError('Failed to access camera. Please try again.');
        await logger.current.error('Unknown error during camera access', { error: String(err) });
      }
      setIsScanning(false);
    }
  };

  const stopScanning = async () => {
    const scanDuration = Date.now() - scanStartTime.current;

    await logger.current.debug('Stopping barcode scanning', { scanDuration });

    scanningRef.current = false;

    // Clean up code reader
    if (codeReader.current) {
      try {
        codeReader.current.reset();
        await logger.current.debug('ZXing code reader reset successfully');
      } catch (error) {
        await logger.current.warn('Error resetting code reader', {
          error: error instanceof Error ? error.message : String(error)
        });
      }
      codeReader.current = null;
    }

    // Clean up video stream
    if (videoRef.current && videoRef.current.srcObject) {
      try {
        const stream = videoRef.current.srcObject as MediaStream;
        const tracks = stream.getTracks();

        await logger.current.debug('Stopping video tracks', { trackCount: tracks.length });

        tracks.forEach(track => {
          track.stop();
        });

        videoRef.current.srcObject = null;
        await logger.current.debug('Video stream cleaned up successfully');
      } catch (error) {
        await logger.current.warn('Error cleaning up video stream', {
          error: error instanceof Error ? error.message : String(error)
        });
      }
    }

    setIsScanning(false);
    await logger.current.info('Barcode scanning session ended', { totalDuration: scanDuration });
  };

  const handleClose = async () => {
    await logger.current.logUserAction('close_barcode_scanner');
    await stopScanning();
    setManualBarcode('');
    onClose();
  };

  const handleManualSubmit = async () => {
    if (manualBarcode.trim()) {
      await logger.current.logUserAction('manual_barcode_submit', {
        code: manualBarcode.trim(),
        codeLength: manualBarcode.trim().length
      });

      onScan(manualBarcode.trim());
      setManualBarcode('');
      onClose();
    }
  };

  const handleManualKeyPress = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter') {
      handleManualSubmit();
    }
  };

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="sm"
      fullWidth
      data-testid="barcode-scanner-dialog"
    >
      <DialogTitle>Scan Barcode</DialogTitle>
      <DialogContent>
        <Box sx={{ textAlign: 'center' }}>
          {hasPermission === null && isScanning && (
            <Box sx={{ mb: 2 }}>
              <CircularProgress size={24} />
              <Typography variant="body2" sx={{ mt: 1 }}>
                Requesting camera permission...
              </Typography>
            </Box>
          )}

          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}

          {hasPermission && !error && (
            <>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                Position the barcode within the camera view. The scan will happen automatically.
              </Typography>
              
              <Box
                sx={{
                  position: 'relative',
                  width: '100%',
                  maxWidth: 400,
                  mx: 'auto',
                  bgcolor: 'black',
                  borderRadius: 1,
                  overflow: 'hidden',
                }}
              >
                <video
                  ref={videoRef}
                  style={{
                    width: '100%',
                    height: 'auto',
                    display: 'block',
                  }}
                  data-testid="barcode-scanner-video"
                />
                
                {/* Scanning overlay */}
                <Box
                  sx={{
                    position: 'absolute',
                    top: '50%',
                    left: '50%',
                    transform: 'translate(-50%, -50%)',
                    width: '80%',
                    height: '60%',
                    border: '2px solid #fff',
                    borderRadius: 1,
                    pointerEvents: 'none',
                    '&::before': {
                      content: '""',
                      position: 'absolute',
                      top: '50%',
                      left: 0,
                      right: 0,
                      height: '2px',
                      bgcolor: 'primary.main',
                      animation: 'scan 2s ease-in-out infinite',
                    },
                  }}
                />
              </Box>
            </>
          )}

          {/* Manual barcode input - always show as fallback */}
          <Box sx={{ mt: error ? 2 : hasPermission ? 3 : 0 }}>
            {(error || hasPermission) && (
              <Divider sx={{ mb: 2 }}>
                <Typography variant="body2" color="text.secondary">
                  OR
                </Typography>
              </Divider>
            )}
            
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              Enter barcode manually:
            </Typography>
            
            <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
              <TextField
                fullWidth
                variant="outlined"
                placeholder="Enter barcode number"
                value={manualBarcode}
                onChange={(e) => setManualBarcode(e.target.value)}
                onKeyPress={handleManualKeyPress}
                data-testid="manual-barcode-input"
                size="small"
              />
              <Button
                variant="contained"
                onClick={handleManualSubmit}
                disabled={!manualBarcode.trim()}
                data-testid="manual-barcode-submit"
              >
                Submit
              </Button>
            </Box>
          </Box>
        </Box>
      </DialogContent>
      <DialogActions>
        <Button onClick={handleClose} data-testid="barcode-scanner-close">
          Close
        </Button>
      </DialogActions>
      
      <style>
        {`
          @keyframes scan {
            0%, 100% { transform: translateY(-100%); }
            50% { transform: translateY(100%); }
          }
        `}
      </style>
    </Dialog>
  );
};

export default BarcodeScanner;
