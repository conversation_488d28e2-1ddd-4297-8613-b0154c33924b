import React, { useEffect, useRef, useState } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  <PERSON>alogActions,
  Button,
  Box,
  Typography,
  Alert,
  CircularProgress,
  TextField,
  Divider,
} from '@mui/material';
import { BrowserMultiFormatReader, NotFoundException } from '@zxing/library';

interface BarcodeScannerProps {
  open: boolean;
  onClose: () => void;
  onScan: (code: string) => void;
}

const BarcodeScanner: React.FC<BarcodeScannerProps> = ({
  open,
  onClose,
  onScan,
}) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const codeReader = useRef<BrowserMultiFormatReader | null>(null);
  const scanningRef = useRef<boolean>(false);
  const [isScanning, setIsScanning] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [hasPermission, setHasPermission] = useState<boolean | null>(null);
  const [manualBarcode, setManualBarcode] = useState('');

  useEffect(() => {
    if (open) {
      startScanning();
    } else {
      stopScanning();
    }

    return () => {
      stopScanning();
    };
  }, [open]);

  const startScanning = async () => {
    try {
      setError(null);
      setIsScanning(true);
      setHasPermission(null);
      scanningRef.current = true;

      // Check if MediaDevices API is available
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        throw new Error('Camera access is not available in this desktop application. Please use the manual barcode input below or open this application in a web browser for camera scanning.');
      }

      // Check for camera permission
      const stream = await navigator.mediaDevices.getUserMedia({ video: true });
      setHasPermission(true);
      
      // Stop the test stream
      stream.getTracks().forEach(track => track.stop());

      // Initialize the code reader with hints for better detection
      codeReader.current = new BrowserMultiFormatReader();
      
      // Configure hints for better barcode detection
      const hints = new Map();
      hints.set(2, true); // POSSIBLE_FORMATS - enable all formats
      hints.set(3, true); // TRY_HARDER - more thorough scanning
      codeReader.current.hints = hints;

      // Get available video devices
      const videoInputDevices = await codeReader.current.listVideoInputDevices();
      
      if (videoInputDevices.length === 0) {
        throw new Error('No camera devices found');
      }

      // Use the first available camera (usually back camera on mobile)
      const selectedDeviceId = videoInputDevices[0].deviceId;

      // Start decoding from video element with constraints for better quality
      if (videoRef.current) {
        const constraints = {
          video: {
            deviceId: selectedDeviceId,
            width: { ideal: 1280, min: 640 },
            height: { ideal: 720, min: 480 },
            facingMode: 'environment' // prefer back camera
          }
        };

        // Start the video stream first
        const stream = await navigator.mediaDevices.getUserMedia(constraints);
        videoRef.current.srcObject = stream;
        
        // Wait for video to be ready
        await new Promise((resolve) => {
          if (videoRef.current) {
            videoRef.current.onloadedmetadata = () => resolve(undefined);
            videoRef.current.play();
          }
        });

        // Start continuous scanning with proper loop
        const scanBarcode = () => {
          if (videoRef.current && codeReader.current && scanningRef.current) {
            codeReader.current.decodeFromVideoElement(videoRef.current)
              .then((result) => {
                if (result && scanningRef.current) {
                  const scannedCode = result.getText();
                  console.log('Barcode detected:', scannedCode);
                  scanningRef.current = false;
                  onScan(scannedCode);
                  stopScanning();
                  onClose();
                } else if (scanningRef.current) {
                  // Continue scanning if no result
                  setTimeout(scanBarcode, 100);
                }
              })
              .catch((error) => {
                if (!(error instanceof NotFoundException)) {
                  console.error('Barcode scanning error:', error);
                }
                // Continue scanning even on errors (except when stopped)
                if (scanningRef.current) {
                  setTimeout(scanBarcode, 100);
                }
              });
          }
        };

        // Start scanning loop
        scanBarcode();
      }
    } catch (err) {
      console.error('Failed to start barcode scanning:', err);
      setHasPermission(false);
      
      if (err instanceof Error) {
        if (err.name === 'NotAllowedError') {
          setError('Camera permission denied. Please allow camera access to scan barcodes.');
        } else if (err.name === 'NotFoundError') {
          setError('No camera found. Please ensure your device has a camera.');
        } else if (err.message.includes('not supported in this environment')) {
          setError(err.message);
        } else {
          setError(`Failed to access camera: ${err.message}`);
        }
      } else {
        setError('Failed to access camera. Please try again.');
      }
      setIsScanning(false);
    }
  };

  const stopScanning = () => {
    scanningRef.current = false;
    if (codeReader.current) {
      codeReader.current.reset();
      codeReader.current = null;
    }
    if (videoRef.current && videoRef.current.srcObject) {
      const stream = videoRef.current.srcObject as MediaStream;
      stream.getTracks().forEach(track => track.stop());
      videoRef.current.srcObject = null;
    }
    setIsScanning(false);
  };

  const handleClose = () => {
    stopScanning();
    setManualBarcode('');
    onClose();
  };

  const handleManualSubmit = () => {
    if (manualBarcode.trim()) {
      onScan(manualBarcode.trim());
      setManualBarcode('');
      onClose();
    }
  };

  const handleManualKeyPress = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter') {
      handleManualSubmit();
    }
  };

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="sm"
      fullWidth
      data-testid="barcode-scanner-dialog"
    >
      <DialogTitle>Scan Barcode</DialogTitle>
      <DialogContent>
        <Box sx={{ textAlign: 'center' }}>
          {hasPermission === null && isScanning && (
            <Box sx={{ mb: 2 }}>
              <CircularProgress size={24} />
              <Typography variant="body2" sx={{ mt: 1 }}>
                Requesting camera permission...
              </Typography>
            </Box>
          )}

          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}

          {hasPermission && !error && (
            <>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                Position the barcode within the camera view. The scan will happen automatically.
              </Typography>
              
              <Box
                sx={{
                  position: 'relative',
                  width: '100%',
                  maxWidth: 400,
                  mx: 'auto',
                  bgcolor: 'black',
                  borderRadius: 1,
                  overflow: 'hidden',
                }}
              >
                <video
                  ref={videoRef}
                  style={{
                    width: '100%',
                    height: 'auto',
                    display: 'block',
                  }}
                  data-testid="barcode-scanner-video"
                />
                
                {/* Scanning overlay */}
                <Box
                  sx={{
                    position: 'absolute',
                    top: '50%',
                    left: '50%',
                    transform: 'translate(-50%, -50%)',
                    width: '80%',
                    height: '60%',
                    border: '2px solid #fff',
                    borderRadius: 1,
                    pointerEvents: 'none',
                    '&::before': {
                      content: '""',
                      position: 'absolute',
                      top: '50%',
                      left: 0,
                      right: 0,
                      height: '2px',
                      bgcolor: 'primary.main',
                      animation: 'scan 2s ease-in-out infinite',
                    },
                  }}
                />
              </Box>
            </>
          )}

          {/* Manual barcode input - always show as fallback */}
          <Box sx={{ mt: error ? 2 : hasPermission ? 3 : 0 }}>
            {(error || hasPermission) && (
              <Divider sx={{ mb: 2 }}>
                <Typography variant="body2" color="text.secondary">
                  OR
                </Typography>
              </Divider>
            )}
            
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              Enter barcode manually:
            </Typography>
            
            <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
              <TextField
                fullWidth
                variant="outlined"
                placeholder="Enter barcode number"
                value={manualBarcode}
                onChange={(e) => setManualBarcode(e.target.value)}
                onKeyPress={handleManualKeyPress}
                data-testid="manual-barcode-input"
                size="small"
              />
              <Button
                variant="contained"
                onClick={handleManualSubmit}
                disabled={!manualBarcode.trim()}
                data-testid="manual-barcode-submit"
              >
                Submit
              </Button>
            </Box>
          </Box>
        </Box>
      </DialogContent>
      <DialogActions>
        <Button onClick={handleClose} data-testid="barcode-scanner-close">
          Close
        </Button>
      </DialogActions>
      
      <style>
        {`
          @keyframes scan {
            0%, 100% { transform: translateY(-100%); }
            50% { transform: translateY(100%); }
          }
        `}
      </style>
    </Dialog>
  );
};

export default BarcodeScanner;
