import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom';
import BarcodeScanner from '../BarcodeScanner';

// Mock the ZXing library
const mockReset = jest.fn();
const mockDecodeFromVideoDevice = jest.fn();
const mockListVideoInputDevices = jest.fn().mockResolvedValue([
  { deviceId: 'camera1', label: 'Camera 1' }
]);

jest.mock('@zxing/library', () => ({
  BrowserMultiFormatReader: jest.fn().mockImplementation(() => ({
    listVideoInputDevices: mockListVideoInputDevices,
    decodeFromVideoDevice: mockDecodeFromVideoDevice,
    reset: mockReset,
  })),
  NotFoundException: class NotFoundException extends Error {
    constructor(message?: string) {
      super(message);
      this.name = 'NotFoundException';
    }
  },
}));

// Mock navigator.mediaDevices
const mockGetUserMedia = jest.fn();
Object.defineProperty(navigator, 'mediaDevices', {
  value: {
    getUserMedia: mockGetUserMedia,
  },
  writable: true,
});

// Mock MediaStreamTrack
const mockTrack = {
  stop: jest.fn(),
};

const mockStream = {
  getTracks: jest.fn(() => [mockTrack]),
};

describe('BarcodeScanner', () => {
  const mockOnClose = jest.fn();
  const mockOnScan = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    mockGetUserMedia.mockResolvedValue(mockStream);
  });

  const renderBarcodeScanner = (open = true) => {
    return render(
      <BarcodeScanner
        open={open}
        onClose={mockOnClose}
        onScan={mockOnScan}
      />
    );
  };

  it('renders when open', () => {
    renderBarcodeScanner();
    expect(screen.getByText('Scan Barcode')).toBeInTheDocument();
    expect(screen.getByTestId('barcode-scanner-close')).toBeInTheDocument();
  });

  it('does not render when closed', () => {
    renderBarcodeScanner(false);
    expect(screen.queryByText('Scan Barcode')).not.toBeInTheDocument();
  });

  it('shows permission request message initially', async () => {
    renderBarcodeScanner();
    
    await waitFor(() => {
      expect(screen.getByText('Requesting camera permission...')).toBeInTheDocument();
    });
  });

  it('shows camera view when permission is granted', async () => {
    renderBarcodeScanner();
    
    await waitFor(() => {
      expect(screen.getByTestId('barcode-scanner-video')).toBeInTheDocument();
    });

    expect(screen.getByText('Position the barcode within the camera view. The scan will happen automatically.')).toBeInTheDocument();
  });

  it('shows error when camera permission is denied', async () => {
    const permissionError = new Error('Permission denied');
    permissionError.name = 'NotAllowedError';
    mockGetUserMedia.mockRejectedValue(permissionError);

    renderBarcodeScanner();
    
    await waitFor(() => {
      expect(screen.getByText('Camera permission denied. Please allow camera access to scan barcodes.')).toBeInTheDocument();
    });
  });

  it('shows error when no camera is found', async () => {
    const cameraError = new Error('No camera found');
    cameraError.name = 'NotFoundError';
    mockGetUserMedia.mockRejectedValue(cameraError);

    renderBarcodeScanner();
    
    await waitFor(() => {
      expect(screen.getByText('No camera found. Please ensure your device has a camera.')).toBeInTheDocument();
    });
  });

  it('shows generic error for other camera errors', async () => {
    const genericError = new Error('Generic camera error');
    mockGetUserMedia.mockRejectedValue(genericError);

    renderBarcodeScanner();
    
    await waitFor(() => {
      expect(screen.getByText('Failed to access camera: Generic camera error')).toBeInTheDocument();
    });
  });

  it('calls onScan when barcode is detected', async () => {
    // Mock successful barcode detection
    mockDecodeFromVideoDevice.mockImplementation((deviceId, video, callback) => {
      // Simulate successful scan
      setTimeout(() => {
        callback({ getText: () => '123456789012' }, null);
      }, 100);
      return Promise.resolve();
    });

    renderBarcodeScanner();
    
    // Wait for the video element to be rendered
    await waitFor(() => {
      expect(screen.getByTestId('barcode-scanner-video')).toBeInTheDocument();
    });

    // Wait for the scan callback to be called
    await waitFor(() => {
      expect(mockOnScan).toHaveBeenCalledWith('123456789012');
    }, { timeout: 2000 });
  });

  it('handles scanning errors gracefully', async () => {
    const { BrowserMultiFormatReader, NotFoundException } = require('@zxing/library');
    const mockReader = new BrowserMultiFormatReader();
    
    // Mock scanning error (NotFoundException should be ignored)
    mockReader.decodeFromVideoDevice.mockImplementation((deviceId, video, callback) => {
      setTimeout(() => {
        callback(null, new NotFoundException('No barcode found'));
      }, 100);
    });

    renderBarcodeScanner();
    
    await waitFor(() => {
      expect(screen.getByTestId('barcode-scanner-video')).toBeInTheDocument();
    });

    // Should not call onScan or onClose for NotFoundException
    expect(mockOnScan).not.toHaveBeenCalled();
    expect(mockOnClose).not.toHaveBeenCalled();
  });

  it('closes scanner when close button is clicked', async () => {
    const user = userEvent.setup();
    renderBarcodeScanner();
    
    const closeButton = screen.getByTestId('barcode-scanner-close');
    await user.click(closeButton);
    
    expect(mockOnClose).toHaveBeenCalled();
  });

  it('stops scanning when component is closed', async () => {
    const { BrowserMultiFormatReader } = require('@zxing/library');
    const mockReader = new BrowserMultiFormatReader();
    
    const { rerender } = renderBarcodeScanner(true);
    
    await waitFor(() => {
      expect(screen.getByTestId('barcode-scanner-video')).toBeInTheDocument();
    });

    // Close the scanner
    rerender(
      <BarcodeScanner
        open={false}
        onClose={mockOnClose}
        onScan={mockOnScan}
      />
    );

    expect(mockReader.reset).toHaveBeenCalled();
  });

  it('handles no camera devices available', async () => {
    const { BrowserMultiFormatReader } = require('@zxing/library');
    const mockReader = new BrowserMultiFormatReader();
    mockReader.listVideoInputDevices.mockResolvedValue([]);

    renderBarcodeScanner();
    
    await waitFor(() => {
      expect(screen.getByText('Failed to access camera: No camera devices found')).toBeInTheDocument();
    });
  });

  it('cleans up resources on unmount', async () => {
    const { BrowserMultiFormatReader } = require('@zxing/library');
    const mockReader = new BrowserMultiFormatReader();
    
    const { unmount } = renderBarcodeScanner();
    
    await waitFor(() => {
      expect(screen.getByTestId('barcode-scanner-video')).toBeInTheDocument();
    });

    unmount();

    expect(mockReader.reset).toHaveBeenCalled();
  });

  it('handles dialog close event', () => {
    renderBarcodeScanner();
    
    // Simulate dialog close event
    fireEvent.keyDown(document, { key: 'Escape' });
    
    expect(mockOnClose).toHaveBeenCalled();
  });

  it('shows scanning animation overlay', async () => {
    renderBarcodeScanner();
    
    await waitFor(() => {
      expect(screen.getByTestId('barcode-scanner-video')).toBeInTheDocument();
    });

    // Check that the scanning overlay is present (it's a styled Box)
    const videoContainer = screen.getByTestId('barcode-scanner-video').parentElement;
    expect(videoContainer).toHaveStyle('position: relative');
  });
});
